import React, { useState, useEffect } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { format } from 'date-fns';
import { CalendarIcon, Clock, User, PawPrint, Save, X, Stethoscope } from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Calendar } from '@/components/ui/calendar';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { useToast } from '@/components/ui/use-toast';
import { cn } from '@/lib/utils';

// Services
import { getAppointmentById, updateAppointment } from '@/services/appointments';
import { getClients } from '@/services/clients';
import { getPets } from '@/services/pets';
import { api } from '@/services/api';

// Appointment categories components
import AppointmentCategorySelector from '@/components/appointments/AppointmentCategorySelector';

// Types
import type { Client, Pet, Staff } from '@/store/types';
import { useAuth } from '@/store';

interface EditAppointmentFormProps {
  appointmentId: string | number;
  onSuccess?: () => void;
  onCancel?: () => void;
}

// Form schema
const editAppointmentSchema = z.object({
  clientId: z.string().min(1, "Client is required"),
  petId: z.string().min(1, "Pet is required"),
  staffInCharge: z.string().min(1, "Staff in charge is required"),
  date: z.date({
    required_error: "Appointment date is required",
  }),
  time: z.string().min(1, "Appointment time is required"),
  duration: z.coerce.number().min(15, "Duration must be at least 15 minutes"),
  priority: z.enum(["low", "normal", "high", "urgent"]).default("normal"),
  status: z.enum(["scheduled", "in_progress", "completed", "cancelled"]).default("scheduled"),
  generalNotes: z.string().optional(),
  recommendations: z.string().optional(),
});

type EditAppointmentFormValues = z.infer<typeof editAppointmentSchema>;

const EditAppointmentForm: React.FC<EditAppointmentFormProps> = ({
  appointmentId,
  onSuccess,
  onCancel
}) => {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const { clinic } = useAuth();

  const [selectedCategories, setSelectedCategories] = useState<Array<{
    appointmentCategoryId: number;
    categoryName: string;
    assignedStaff?: number;
    assignedStaffName?: string;
    estimatedDuration: number;
    categoryServices: Array<{
      categoryServiceId: number;
      serviceName: string;
      price: number;
      currency: string;
      notes?: string;
    }>;
    categoryNotes?: string;
  }>>([]);

  const [timeOptions, setTimeOptions] = useState<string[]>([]);

  // Generate time options (30-minute intervals)
  useEffect(() => {
    const times = [];
    for (let i = 8; i < 18; i++) {
      times.push(`${i}:00`);
      times.push(`${i}:30`);
    }
    setTimeOptions(times);
  }, []);

  // Initialize form
  const form = useForm<EditAppointmentFormValues>({
    resolver: zodResolver(editAppointmentSchema),
    defaultValues: {
      clientId: "",
      petId: "",
      staffInCharge: "",
      duration: 30,
      priority: "normal",
      status: "scheduled",
      generalNotes: "",
      recommendations: "",
    },
  });

  // Fetch appointment data
  const { data: appointmentResponse, isLoading: isLoadingAppointment } = useQuery({
    queryKey: ['appointment', appointmentId],
    queryFn: () => getAppointmentById(appointmentId),
    enabled: !!appointmentId
  });

  const appointment = appointmentResponse?.data;

  // Fetch clients
  const { data: clientsResponse } = useQuery({
    queryKey: ['clients'],
    queryFn: () => getClients({ limit: 100 }),
  });

  // Fetch pets for selected client
  const { data: petsResponse } = useQuery({
    queryKey: ['pets', form.watch('clientId')],
    queryFn: () => getPets({
      clientId: form.watch('clientId'),
      limit: 100
    }),
    enabled: !!form.watch('clientId')
  });

  // Fetch clinic staff (clinic-specific)
  const { data: staffResponse } = useQuery({
    queryKey: ['clinic-staff', appointment?.clinicId],
    queryFn: async () => {
      // Get clinicId from appointment data or auth context
      const clinicId = appointment?.clinicId || clinic?.clinicId;
      if (!clinicId) {
        console.warn('No clinic ID available for staff fetching');
        return { data: { data: [] } };
      }
      const response = await api.get(`/staff?clinicId=${clinicId}&status=1&limit=100`);
      return response;
    },
    enabled: !!appointment?.clinicId || !!clinic?.clinicId
  });

  const clients = clientsResponse?.data?.data || [];
  const pets = petsResponse?.data?.data || [];
  const staff = staffResponse?.data?.data || [];

  // Populate form when appointment data is loaded
  useEffect(() => {
    if (appointment) {
      // Parse appointment date and time
      const appointmentDate = new Date(appointment.appointmentDate);
      const timeString = format(appointmentDate, 'H:mm');

      form.reset({
        clientId: appointment.clientId?.toString() || "",
        petId: appointment.petId?.toString() || "",
        staffInCharge: appointment.staffInCharge?.toString() || "",
        date: appointmentDate,
        time: timeString,
        duration: appointment.estimatedDuration || 30,
        priority: appointment.priority || "normal",
        status: appointment.status || "scheduled",
        generalNotes: appointment.generalNotes || "",
        recommendations: appointment.recommendations || "",
      });

      // Set selected categories
      if (appointment.appointmentCategories) {
        setSelectedCategories(appointment.appointmentCategories);
      }
    }
  }, [appointment, form]);

  // Update appointment mutation
  const updateAppointmentMutation = useMutation({
    mutationFn: (data: any) => updateAppointment(appointmentId.toString(), data),
    onSuccess: () => {
      toast({
        title: "Success",
        description: "Appointment updated successfully",
      });
      queryClient.invalidateQueries({ queryKey: ["appointments"] });
      queryClient.invalidateQueries({ queryKey: ["appointment", appointmentId] });
      onSuccess?.();
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.message || "Failed to update appointment",
        variant: "destructive",
      });
    },
  });

  const onSubmit = (data: EditAppointmentFormValues) => {
    // Validate appointment categories
    if (selectedCategories.length === 0) {
      toast({
        title: "Error",
        description: "Please select at least one appointment category",
        variant: "destructive",
      });
      return;
    }

    // Parse time string to create a full date-time
    const [hours, minutes] = data.time.split(":").map(Number);
    const dateTime = new Date(data.date);
    dateTime.setHours(hours, minutes, 0, 0);

    // Calculate total estimated duration from selected categories
    const totalDuration = selectedCategories.reduce((total, category) => {
      return total + category.estimatedDuration;
    }, 0);

    const updateData = {
      clientId: parseInt(data.clientId),
      petId: parseInt(data.petId),
      staffInCharge: parseInt(data.staffInCharge),
      appointmentDate: dateTime,
      estimatedDuration: totalDuration || data.duration,
      priority: data.priority,
      status: data.status,
      generalNotes: data.generalNotes,
      recommendations: data.recommendations,
      appointmentCategories: selectedCategories,
      clinicId: clinic?.clinicId || appointment?.clinicId || 1001,
    };

    updateAppointmentMutation.mutate(updateData);
  };

  if (isLoadingAppointment) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Loading Appointment...</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center p-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!appointment) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Appointment Not Found</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-gray-600">The requested appointment could not be found.</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Clock className="h-5 w-5" />
          Edit Appointment
        </CardTitle>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Client Selection */}
              <FormField
                control={form.control}
                name="clientId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Client</FormLabel>
                    <Select onValueChange={field.onChange} value={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select client" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {clients.map((client: Client) => (
                          <SelectItem key={client.clientId} value={client.clientId?.toString() || ""}>
                            {client.firstName} {client.lastName}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Pet Selection */}
              <FormField
                control={form.control}
                name="petId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Pet</FormLabel>
                    <Select onValueChange={field.onChange} value={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select pet" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {pets.map((pet: Pet) => (
                          <SelectItem key={pet.petId} value={pet.petId?.toString() || ""}>
                            {pet.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Date and Time */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <FormField
                control={form.control}
                name="date"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Appointment Date</FormLabel>
                    <Popover>
                      <PopoverTrigger asChild>
                        <FormControl>
                          <Button
                            variant={"outline"}
                            className={cn(
                              "w-full pl-3 text-left font-normal",
                              !field.value && "text-muted-foreground"
                            )}
                          >
                            <CalendarIcon className="mr-2 h-4 w-4" />
                            {field.value ? (
                              format(field.value, "PPP")
                            ) : (
                              <span>Select date</span>
                            )}
                          </Button>
                        </FormControl>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0" align="start">
                        <Calendar
                          mode="single"
                          selected={field.value}
                          onSelect={field.onChange}
                          initialFocus
                        />
                      </PopoverContent>
                    </Popover>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="time"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Appointment Time</FormLabel>
                    <Select onValueChange={field.onChange} value={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select time" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {timeOptions.map((time) => (
                          <SelectItem key={time} value={time}>
                            {time}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Staff, Duration, Priority, Status */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <FormField
                control={form.control}
                name="staffInCharge"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Staff in Charge</FormLabel>
                    <Select onValueChange={field.onChange} value={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select staff" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {staff.map((member: Staff) => (
                          <SelectItem key={member.staffId} value={member.staffId?.toString() || ""}>
                            {member.firstName} {member.lastName}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="duration"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Duration (min)</FormLabel>
                    <FormControl>
                      <Input type="number" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="priority"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Priority</FormLabel>
                    <Select onValueChange={field.onChange} value={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="low">Low</SelectItem>
                        <SelectItem value="normal">Normal</SelectItem>
                        <SelectItem value="high">High</SelectItem>
                        <SelectItem value="urgent">Urgent</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="status"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Status</FormLabel>
                    <Select onValueChange={field.onChange} value={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="scheduled">Scheduled</SelectItem>
                        <SelectItem value="in_progress">In Progress</SelectItem>
                        <SelectItem value="completed">Completed</SelectItem>
                        <SelectItem value="cancelled">Cancelled</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <Separator />

            {/* Appointment Categories */}
            <div>
              <Label className="text-base font-semibold">Appointment Categories</Label>
              <p className="text-sm text-gray-600 mb-4">Update categories for this appointment. Services are managed in the workflow.</p>
              <AppointmentCategorySelector
                selectedCategories={selectedCategories}
                onCategoriesChange={setSelectedCategories}
                disabled={false}
                mode="categories-only"
              />
            </div>

            <Separator />

            {/* Notes */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <FormField
                control={form.control}
                name="generalNotes"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>General Notes</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="General notes about the appointment"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="recommendations"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Recommendations</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Recommendations for the client"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Action Buttons */}
            <div className="flex justify-end space-x-2">
              <Button
                type="button"
                variant="outline"
                onClick={onCancel}
              >
                <X className="h-4 w-4 mr-2" />
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={updateAppointmentMutation.isPending}
              >
                <Save className="h-4 w-4 mr-2" />
                {updateAppointmentMutation.isPending ? "Updating..." : "Update Appointment"}
              </Button>
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
};

export default EditAppointmentForm;
