import React, { useState, useEffect } from 'react';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import {
  Users,
  Clock,
  CheckCircle,
  User,
  Stethoscope,
  Settings,
  Save
} from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { useToast } from '@/components/ui/use-toast';
import { api } from '@/services/api';
import { getAppointmentCategories } from '@/services/appointmentCategories';
import StaffSelectionModal from './StaffSelectionModal';

interface TaskAssignmentProps {
  appointmentId: number;
  selectedAppointmentTypes: any[];
  appointmentData?: any; // Add appointment data to access appointmentCategories
  clinicId?: number;
  onAssignmentComplete?: () => void;
}

interface CategoryAssignment {
  appointmentCategoryId: number;
  categoryName: string;
  staffAssigned: number;
  staffAssignedName?: string;
  estimatedDuration: number;
  priority: 'low' | 'normal' | 'high' | 'urgent';
  isSelected: boolean;
}

const TaskAssignment: React.FC<TaskAssignmentProps> = ({
  appointmentId,
  selectedAppointmentTypes,
  appointmentData,
  clinicId,
  onAssignmentComplete
}) => {
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const [availableCategories, setAvailableCategories] = useState<CategoryAssignment[]>([]);
  const [vetInCharge, setVetInCharge] = useState<string>('');
  const [showStaffModal, setShowStaffModal] = useState(false);
  const [selectedCategoryForStaff, setSelectedCategoryForStaff] = useState<number | null>(null);

  // Fetch all available appointment categories
  const { data: appointmentCategoriesResponse } = useQuery({
    queryKey: ['appointmentCategories'],
    queryFn: () => getAppointmentCategories({ isActive: true })
  });

  const allCategories = appointmentCategoriesResponse?.data || [];

  // Initialize categories when data is available
  useEffect(() => {
    if (allCategories.length > 0) {
      const categoryAssignments: CategoryAssignment[] = allCategories.map((category: any) => {
        // Check if this category is already assigned in the appointment
        const existingCategory = appointmentData?.appointmentCategories?.find(
          (ac: any) => ac.appointmentCategoryId === category.appointmentCategoryId
        );

        // Check if this category was selected during appointment creation
        const wasPreselected = appointmentData?.appointmentCategories?.some(
          (ac: any) => ac.appointmentCategoryId === category.appointmentCategoryId
        );

        return {
          appointmentCategoryId: category.appointmentCategoryId,
          categoryName: category.name,
          staffAssigned: existingCategory?.staffAssigned || 0,
          staffAssignedName: existingCategory?.staffAssignedName || '',
          estimatedDuration: existingCategory?.estimatedDuration || category.estimatedDuration || 30,
          priority: existingCategory?.priority || 'normal',
          isSelected: !!wasPreselected
        };
      });

      setAvailableCategories(categoryAssignments);
    }

    // Set veterinarian in charge if it exists
    if (appointmentData?.staffInCharge) {
      setVetInCharge(appointmentData.staffInCharge.toString());
    }
  }, [allCategories, appointmentData]);

  // Fetch clinic staff data
  const { data: staffResponse } = useQuery({
    queryKey: ['clinic-staff', clinicId],
    queryFn: async () => {
      if (!clinicId) {
        console.warn('No clinic ID available for staff fetching in TaskAssignment');
        return { data: { data: [] } };
      }
      console.log(`Fetching staff for clinic: ${clinicId}`);
      return await api.get(`/staff?clinicId=${clinicId}&status=1&limit=100`);
    },
    enabled: !!clinicId
  });

  const staff = staffResponse?.data?.data || [];
  const veterinarians = staff.filter((s: any) =>
    s.jobTitle?.toLowerCase().includes('vet') ||
    s.jobTitle?.toLowerCase().includes('doctor')
  );

  // Assign categories mutation
  const assignCategoriesMutation = useMutation({
    mutationFn: async (data: { selectedCategories: CategoryAssignment[]; vetInCharge: number }) => {
      const response = await api.post(`/appointments/${appointmentId}/assign-categories`, {
        ...data,
        clinicId,
        appointmentId
      });
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['appointment', appointmentId] });
      toast({
        title: "Success",
        description: "Categories assigned successfully",
      });
      if (onAssignmentComplete) {
        onAssignmentComplete();
      }
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.response?.data?.message || "Failed to assign categories",
        variant: "destructive",
      });
    }
  });

  // Handler functions
  const handleCategoryToggle = (categoryId: number) => {
    setAvailableCategories(prev => prev.map(cat =>
      cat.appointmentCategoryId === categoryId
        ? { ...cat, isSelected: !cat.isSelected }
        : cat
    ));
  };

  const handleStaffAssignment = (categoryId: number) => {
    setSelectedCategoryForStaff(categoryId);
    setShowStaffModal(true);
  };

  const handleStaffSelect = (staffId: number, staffName: string) => {
    if (selectedCategoryForStaff) {
      setAvailableCategories(prev => prev.map(cat =>
        cat.appointmentCategoryId === selectedCategoryForStaff
          ? { ...cat, staffAssigned: staffId, staffAssignedName: staffName }
          : cat
      ));
    }
    setSelectedCategoryForStaff(null);
  };

  const updateCategoryDuration = (categoryId: number, duration: number) => {
    setAvailableCategories(prev => prev.map(cat =>
      cat.appointmentCategoryId === categoryId
        ? { ...cat, estimatedDuration: duration }
        : cat
    ));
  };

  const handleAssignCategories = () => {
    const selectedCategories = availableCategories.filter(cat => cat.isSelected);

    if (selectedCategories.length === 0) {
      toast({
        title: "Error",
        description: "Please select at least one category",
        variant: "destructive",
      });
      return;
    }

    if (!vetInCharge) {
      toast({
        title: "Error",
        description: "Please select a veterinarian in charge",
        variant: "destructive",
      });
      return;
    }

    // Validate all selected categories have staff assigned
    const unassignedCategories = selectedCategories.filter(cat => !cat.staffAssigned || cat.staffAssigned === 0);
    if (unassignedCategories.length > 0) {
      toast({
        title: "Error",
        description: "Please assign staff to all selected categories",
        variant: "destructive",
      });
      return;
    }

    assignCategoriesMutation.mutate({
      selectedCategories,
      vetInCharge: parseInt(vetInCharge)
    });
  };

  const selectedCategories = availableCategories.filter(cat => cat.isSelected);

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Users className="h-5 w-5" />
          Task Assignment
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Veterinarian in Charge Selection */}
        <div className="space-y-2">
          <Label className="flex items-center gap-2">
            <Stethoscope className="h-4 w-4" />
            Veterinarian in Charge
            {appointmentData?.staffInCharge && (
              <Badge variant="outline" className="text-xs bg-blue-50 text-blue-700 border-blue-200">
                Currently Assigned
              </Badge>
            )}
          </Label>
          <Select value={vetInCharge} onValueChange={setVetInCharge}>
            <SelectTrigger>
              <SelectValue placeholder="Select veterinarian in charge" />
            </SelectTrigger>
            <SelectContent>
              {veterinarians.map((vet: any) => (
                <SelectItem key={vet.staffId} value={vet.staffId.toString()}>
                  Dr. {vet.firstName} {vet.lastName} - {vet.jobTitle}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Categories Selection */}
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <Label>Select Categories for Assignment</Label>
            <Badge variant="outline" className="text-xs">
              {selectedCategories.length} selected
            </Badge>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
            {availableCategories.map((category) => (
              <div
                key={category.appointmentCategoryId}
                className={`p-3 border rounded-lg cursor-pointer transition-all ${
                  category.isSelected
                    ? 'border-blue-500 bg-blue-50'
                    : 'border-gray-200 hover:border-gray-300'
                }`}
                onClick={() => handleCategoryToggle(category.appointmentCategoryId)}
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Stethoscope className="h-4 w-4 text-gray-600" />
                    <span className="text-sm font-medium">{category.categoryName}</span>
                  </div>
                  {category.isSelected && (
                    <CheckCircle className="h-4 w-4 text-blue-500" />
                  )}
                </div>
                <div className="mt-2 text-xs text-gray-500">
                  {category.estimatedDuration} min
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Staff Assignment for Selected Categories */}
        {selectedCategories.length > 0 && (
          <div className="space-y-4">
            <Label>Assign Staff to Selected Categories</Label>
            <div className="space-y-3">
              {selectedCategories.map((category) => (
                <Card key={category.appointmentCategoryId} className="p-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <Stethoscope className="h-4 w-4 text-blue-600" />
                      <span className="font-medium">{category.categoryName}</span>
                      <Badge variant="outline" className="text-xs">
                        {category.estimatedDuration} min
                      </Badge>
                    </div>
                    <div className="flex items-center gap-2">
                      {category.staffAssignedName ? (
                        <Badge variant="default" className="text-xs">
                          {category.staffAssignedName}
                        </Badge>
                      ) : (
                        <Badge variant="outline" className="text-xs text-orange-600">
                          No staff assigned
                        </Badge>
                      )}
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => handleStaffAssignment(category.appointmentCategoryId)}
                        className="flex items-center gap-1"
                      >
                        <Settings className="h-3 w-3" />
                        {category.staffAssignedName ? 'Change' : 'Assign'} Staff
                      </Button>
                    </div>
                  </div>

                  <div className="mt-3 flex items-center gap-4">
                    <div className="flex items-center gap-2">
                      <Clock className="h-3 w-3 text-gray-500" />
                      <Label className="text-xs">Duration:</Label>
                      <Input
                        type="number"
                        value={category.estimatedDuration}
                        onChange={(e) => updateCategoryDuration(category.appointmentCategoryId, parseInt(e.target.value))}
                        min="5"
                        max="300"
                        className="w-20 h-7 text-xs"
                      />
                      <span className="text-xs text-gray-500">minutes</span>
                    </div>
                  </div>
                </Card>
              ))}
            </div>
          </div>
        )}

        {/* Assignment Summary */}
        {selectedCategories.length > 0 && (
          <Card className="bg-blue-50 border-blue-200">
            <CardContent className="p-4">
              <div className="flex items-center gap-2 mb-2">
                <Users className="h-4 w-4 text-blue-600" />
                <span className="font-medium text-blue-800">Assignment Summary</span>
              </div>
              <div className="text-sm text-blue-700">
                <p>Categories: {selectedCategories.length}</p>
                <p>Total Estimated Duration: {selectedCategories.reduce((sum, cat) => sum + cat.estimatedDuration, 0)} minutes</p>
                <p>Staff Assigned: {selectedCategories.filter(cat => cat.staffAssigned).length}/{selectedCategories.length}</p>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Staff Selection Modal */}
        <StaffSelectionModal
          isOpen={showStaffModal}
          onClose={() => setShowStaffModal(false)}
          onSelect={handleStaffSelect}
          staff={staff}
          title="Select Staff Member"
          currentSelection={selectedCategoryForStaff ? availableCategories.find(cat => cat.appointmentCategoryId === selectedCategoryForStaff)?.staffAssigned : undefined}
        />

        {/* Action Buttons */}
        <div className="flex gap-3 pt-4">
          <Button
            onClick={handleAssignCategories}
            disabled={assignCategoriesMutation.isPending || selectedCategories.length === 0}
            className="flex-1"
          >
            <Save className="h-4 w-4 mr-2" />
            {assignCategoriesMutation.isPending ? 'Assigning...' : 'Save Task Assignments'}
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};

export default TaskAssignment;
