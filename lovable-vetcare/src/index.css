
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 210 40% 98%;
    --foreground: 222 47% 11%;
    --card: 0 0% 100%;
    --card-foreground: 222 47% 11%;
    --popover: 0 0% 100%;
    --popover-foreground: 222 47% 11%;
    --primary: 262 83% 58%;
    --primary-foreground: 210 40% 98%;
    --secondary: 199 89% 48%;
    --secondary-foreground: 210 40% 98%;
    --accent: 262 83% 58%;
    --accent-foreground: 210 40% 98%;
    --muted: 210 40% 96%;
    --muted-foreground: 215 16% 47%;
    --destructive: 0 84% 60%;
    --destructive-foreground: 210 40% 98%;
    --border: 214 32% 91%;
    --input: 214 32% 91%;
    --ring: 262 83% 58%;
    --radius: 1rem;
    --sidebar: 262 60% 98%;
  }

  .dark {
    --background: 224 71% 4%;
    --foreground: 213 31% 91%;
    --card: 224 71% 4%;
    --card-foreground: 213 31% 91%;
    --popover: 224 71% 4%;
    --popover-foreground: 213 31% 91%;
    --primary: 263 85% 60%;
    --primary-foreground: 210 40% 98%;
    --secondary: 199 89% 48%;
    --secondary-foreground: 210 40% 98%;
    --muted: 223 47% 11%;
    --muted-foreground: 215 20% 65%;
    --accent: 263 85% 60%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 63% 31%;
    --destructive-foreground: 210 40% 98%;
    --border: 216 34% 17%;
    --input: 216 34% 17%;
    --ring: 263 85% 60%;
    --sidebar: 224 71% 8%;
  }
}

@layer base {
  * {
    @apply border-border antialiased;
  }
  body {
    @apply bg-background text-foreground;
  }
  .dark body {
    @apply bg-[#0A0A0A];
  }
  .dark main {
    @apply bg-[#0A0A0A];
  }
  .dark div {
    @apply text-gray-100;
  }

  /* High contrast text improvements for dark mode */
  .dark label,
  .dark h1,
  .dark h2,
  .dark h3,
  .dark h4,
  .dark h5,
  .dark h6 {
    @apply text-white;
  }

  .dark input::placeholder,
  .dark textarea::placeholder {
    @apply text-gray-400;
  }

  .dark input,
  .dark textarea,
  .dark select {
    @apply bg-gray-800 border-gray-700 text-white;
  }

  .dark .form-message {
    @apply text-red-400;
  }

  /* Primary color adjustments for dark mode */
  .dark .text-primary {
    @apply text-blue-400;
  }

  .dark .bg-primary {
    @apply bg-blue-600;
  }

  .dark .hover\:bg-primary:hover {
    @apply hover:bg-blue-700;
  }

  /* 3D transform styles */
  .perspective-1000 {
    perspective: 1000px;
  }

  .transform-3d {
    transform-style: preserve-3d;
  }

  .backface-hidden {
    backface-visibility: hidden;
  }
}

/* Custom animations */
.fade-in {
  animation: fadeIn 0.5s ease-out;
}

.scale-in {
  animation: scaleIn 0.3s ease-out;
}

@keyframes scaleIn {
  from {
    transform: scale(0.95);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

.animate-on-scroll {
  opacity: 0;
  transform: translateY(20px);
  transition: all 0.6s ease-out;
}

.animate-on-scroll.animate-fade-in {
  opacity: 1;
  transform: translateY(0);
}

@keyframes float {
  0% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
  100% {
    transform: translateY(0px);
  }
}

.floating {
  animation: float 6s ease-in-out infinite;
}

@keyframes heartbeat {
  0% {
    transform: scale(1);
  }
  14% {
    transform: scale(1.3);
  }
  28% {
    transform: scale(1);
  }
  42% {
    transform: scale(1.3);
  }
  70% {
    transform: scale(1);
  }
}

.animate-heartbeat {
  animation: heartbeat 1.5s ease-in-out infinite;
}

/* Dark mode focus improvements */
.dark :focus-visible {
  @apply outline-white ring-offset-gray-900;
}

/* Glassmorphism for dark mode */
.dark .glass-card {
  @apply bg-gray-900/70 backdrop-blur-lg border border-gray-800;
}

/* Tooltip positioning fixes */
[data-radix-popper-content-wrapper] {
  z-index: 9999 !important;
  position: fixed !important;
}

/* Ensure tooltips are not clipped by sidebar */
.sidebar-tooltip {
  position: fixed !important;
  z-index: 9999 !important;
  pointer-events: none;
}

/* Add sidebar styling */
.sidebar {
  @apply bg-sidebar border-r border-border;
}

.dark .sidebar {
  @apply bg-sidebar border-border;
}
