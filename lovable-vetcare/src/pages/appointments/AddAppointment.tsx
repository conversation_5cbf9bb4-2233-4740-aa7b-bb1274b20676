import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Search, X, CalendarIcon, Clock, UserPlus, PawPrint, Users, Stethoscope } from "lucide-react";
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { QuickClientModal } from "./components/QuickClientModal";
import { QuickPetModal } from "./components/QuickPetModal";
import { useState, useEffect } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { getClients } from "@/services/clients";
import { getPets } from "@/services/pets";
import { createAppointment, getAppointmentById, updateAppointment } from "@/services/appointments";
import { useSpeciesQuery, useBreedsBySpeciesQuery } from "@/hooks/useReferenceDataQuery";
import { Client, Pet, Appointment, AppointmentType, Staff } from "@/store/types";
import { useParams, useNavigate } from "react-router-dom";
import { Calendar } from "@/components/ui/calendar";
import { format } from "date-fns";
import { useToast } from "@/components/ui/use-toast";
import { useAuth } from "@/store";
import { z } from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { cn } from "@/lib/utils";
import { Textarea } from "@/components/ui/textarea";
import { getAppointmentTypes } from "@/services/appointments";
import AppointmentCategorySelector from "@/components/appointments/AppointmentCategorySelector";
import { api } from "@/services/api";

const AddAppointment = ({
  open = true,
  onOpenChange = () => {},
  onSuccess,
}: {
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
  onSuccess?: () => void;
} = {}) => {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const { clinic } = useAuth();
  const { id } = useParams();
  const navigate = useNavigate();
  const isEditMode = !!id;

  const [searchOpen, setSearchOpen] = useState({
    client: false,
    pet: false
  });
  const [searchQuery, setSearchQuery] = useState({
    clientName: '',
    phone: '',
    petName: '',
    speciesId: '',
    breedId: ''
  });

  // State for multiple pets in walk-in appointments
  const [walkInPets, setWalkInPets] = useState([
    {
      id: 1,
      petName: '',
      speciesId: '',
      breedId: '',
      gender: 'male',
      age: '',
      weight: ''
    }
  ]);
  const [selectedClient, setSelectedClient] = useState<Client | null>(null);
  const [selectedPet, setSelectedPet] = useState<Pet | null>(null);
  const [timeOptions, setTimeOptions] = useState<string[]>([]);
  const [appointmentType, setAppointmentType] = useState<'existing' | 'walk-in'>('existing');
  const [showQuickClientModal, setShowQuickClientModal] = useState(false);
  const [showQuickPetModal, setShowQuickPetModal] = useState(false);
  const [selectedAppointmentTypes, setSelectedAppointmentTypes] = useState<string[]>([]);
  const [selectedCategories, setSelectedCategories] = useState<Array<{
    appointmentCategoryId: number;
    categoryName: string;
    assignedStaff?: number;
    assignedStaffName?: string;
    estimatedDuration: number;
    categoryServices: Array<{
      categoryServiceId: number;
      serviceName: string;
      price: number;
      currency: string;
      notes?: string;
    }>;
    categoryNotes?: string;
  }>>([]);

  // Define appointment form schema with strict validation for create appointment
  const appointmentFormSchema = z.object({
    clientId: z.string().min(1, "Client is required for existing appointments"),
    petId: z.string().min(1, "Pet is required for existing appointments"),
    vetId: z.string().min(1, "Veterinarian is required"),
    date: z.date({
      required_error: "Appointment date is required",
    }),
    time: z.string().min(1, "Appointment time is required"),
    duration: z.coerce.number().min(15, "Duration must be at least 15 minutes"),
    appointmentTypes: z.array(z.string()).optional(), // Keep for backward compatibility
    reason: z.string().min(3, "Reason for visit is required"),
    notes: z.string().optional(),
    status: z.enum(["scheduled", "completed", "cancelled"]).default("scheduled"),
    priority: z.enum(["low", "normal", "high", "urgent"]).default("normal"),
  }).refine((data) => {
    // Additional validation based on appointment type
    if (appointmentType === 'existing') {
      return data.clientId && data.petId;
    }
    return true; // Walk-in appointments have different validation
  }, {
    message: "Client and pet are required for existing appointments",
    path: ["clientId"]
  });

  type AppointmentFormValues = z.infer<typeof appointmentFormSchema>;

  // Initialize form with default values
  const form = useForm<AppointmentFormValues>({
    resolver: zodResolver(appointmentFormSchema),
    defaultValues: {
      clientId: "",
      petId: "",
      vetId: "",
      duration: 30,
      appointmentTypes: [],
      reason: "",
      notes: "",
      status: "scheduled",
      priority: "normal",
    },
  });

  // Generate time options (30-minute intervals) with "Now" option
  useEffect(() => {
    const times = ["now"]; // Add "Now" option first
    for (let i = 8; i < 18; i++) {
      times.push(`${i}:00`);
      times.push(`${i}:30`);
    }
    setTimeOptions(times);
  }, []);

  // Client search query
  const { data: clientsData } = useQuery({
    queryKey: ['clients', searchQuery.clientName, searchQuery.phone],
    queryFn: () => getClients({
      name: searchQuery.clientName,
      phoneNumber: searchQuery.phone,
      limit: 5
    }),
    enabled: !!(searchQuery.clientName || searchQuery.phone)
  });

  // Pet query - fetch all pets for the selected client
  const { data: petsData } = useQuery({
    queryKey: ['pets', selectedClient?.clientId],
    queryFn: () => getPets({
      clientId: selectedClient?.clientId?.toString(),
      limit: 100
    }),
    enabled: !!selectedClient?.clientId
  });

  // Staff query for veterinarians - clinic-specific
  const { data: staffResponse, isLoading: isLoadingStaff } = useQuery({
    queryKey: ['clinic-staff', clinic?.clinicId],
    queryFn: async () => {
      const clinicId = clinic?.clinicId;
      if (!clinicId) {
        console.warn('No clinic ID available for staff fetching in AddAppointment');
        return { data: { data: [] } };
      }
      console.log(`Fetching staff for clinic: ${clinicId} in AddAppointment`);
      return await api.get(`/staff?clinicId=${clinicId}&status=1&limit=100`);
    },
    enabled: !!clinic?.clinicId
  });

  const staff = staffResponse?.data?.data || [];

  // Fetch appointment data for edit mode
  const { data: appointmentData } = useQuery({
    queryKey: ['appointment', id],
    queryFn: () => getAppointmentById(id!),
    enabled: isEditMode && !!id
  });

  // Add this query to fetch appointment types
  const { data: appointmentTypesData } = useQuery({
    queryKey: ['appointmentTypes'],
    queryFn: () => getAppointmentTypes(),
    enabled: open
  });

  // Species query for walk-in pets - using centralized reference data
  const { data: species, isLoading: isLoadingSpecies } = useSpeciesQuery();

  // Breeds query for walk-in pets - using centralized reference data
  const { data: breeds, isLoading: isLoadingBreeds } = useBreedsBySpeciesQuery(
    appointmentType === 'walk-in' ? searchQuery.speciesId : null
  );

  const clients = clientsData?.data?.data || [];
  const pets = petsData?.data?.data || [];
  // staff is already available from useStaffQuery
  const appointmentTypes = appointmentTypesData?.data?.categories || [];
  // species and breeds are already available from reference data hooks

  // Create appointment mutation
  const createAppointmentMutation = useMutation({
    mutationFn: (data: Omit<Appointment, never>) => createAppointment(data),
    onSuccess: () => {
      toast({
        title: "Success",
        description: "Appointment created successfully",
      });
      queryClient.invalidateQueries({ queryKey: ["appointments"] });
      onSuccess?.();
      onOpenChange(false);
      form.reset();
      setSelectedClient(null);
      setSelectedPet(null);
      if (!isEditMode) {
        navigate('/appointments');
      }
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: "Failed to create appointment",
        variant: "destructive",
      });
      console.error("Error creating appointment:", error);
    },
  });

  // Update appointment mutation
  const updateAppointmentMutation = useMutation({
    mutationFn: (data: any) => updateAppointment(id!, data),
    onSuccess: () => {
      toast({
        title: "Success",
        description: "Appointment updated successfully",
      });
      queryClient.invalidateQueries({ queryKey: ["appointments"] });
      queryClient.invalidateQueries({ queryKey: ["appointment", id] });
      navigate('/appointments');
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: "Failed to update appointment",
        variant: "destructive",
      });
      console.error("Error updating appointment:", error);
    },
  });

  const handleClientSearch = () => {
    // Trigger client search
    setSearchOpen(prev => ({ ...prev, client: true }));
  };

  const handlePetSearch = () => {
    // Trigger pet search
    setSearchOpen(prev => ({ ...prev, pet: true }));
  };

  const handleClientSelect = (client: Client) => {
    setSelectedClient(client);
    setSearchOpen(prev => ({ ...prev, client: false }));
    form.setValue("clientId", client.clientId?.toString() || "");
    // Reset pet selection when client changes
    setSelectedPet(null);
    form.setValue("petId", "");
  };

  const handlePetSelect = (pet: Pet) => {
    setSelectedPet(pet);
    setSearchOpen(prev => ({ ...prev, pet: false }));
    form.setValue("petId", pet.petId?.toString() || "");
  };

  // Function to fetch pets for a client (for quick add functionality)
  const fetchPetsForClient = (clientId: string | number) => {
    // This will trigger the pets query to refetch
    queryClient.invalidateQueries({ queryKey: ['pets', clientId] });
  };

  const handleQuickClientCreated = (client: any) => {
    setSelectedClient(client);
    form.setValue("clientId", client.clientId);
    setShowQuickClientModal(false);

    // Fetch pets for this client
    if (client._id || client.clientId) {
      fetchPetsForClient(client._id || client.clientId);
    }
  };

  const handleQuickPetCreated = (pet: any) => {
    setSelectedPet(pet);
    form.setValue("petId", pet.petId?.toString() || "");
    setShowQuickPetModal(false);

    // Refresh pets list
    if (selectedClient) {
      fetchPetsForClient(selectedClient.clientId?.toString() || "");
    }
  };

  const handleAppointmentTypeToggle = (typeId: string) => {
    const currentTypes = form.getValues("appointmentTypes");
    const isSelected = currentTypes.includes(typeId);

    let newTypes: string[];
    if (isSelected) {
      newTypes = currentTypes.filter(id => id !== typeId);
    } else {
      newTypes = [...currentTypes, typeId];
    }

    form.setValue("appointmentTypes", newTypes);
    setSelectedAppointmentTypes(newTypes);

    // Update duration based on selected types (use the maximum duration)
    if (newTypes.length > 0) {
      const selectedTypesData = appointmentTypes.filter((type: any) =>
        newTypes.includes(type.categoryId?.toString())
      );
      const maxDuration = Math.max(...selectedTypesData.map((type: any) => type.estimatedDuration || 30));
      form.setValue("duration", maxDuration);
    }
  };

  const handleDateSelect = (date: Date | undefined) => {
    if (date) {
      // Reset time when date changes
      form.setValue("time", "");
    }
  };

  // Functions for managing multiple pets in walk-in appointments
  const addWalkInPet = () => {
    const newPet = {
      id: Date.now(),
      petName: '',
      speciesId: '',
      breedId: '',
      gender: 'male',
      age: '',
      weight: ''
    };
    setWalkInPets(prev => [...prev, newPet]);
  };

  const removeWalkInPet = (petId: number) => {
    if (walkInPets.length > 1) {
      setWalkInPets(prev => prev.filter(pet => pet.id !== petId));
    }
  };

  const updateWalkInPet = (petId: number, field: string, value: string) => {
    setWalkInPets(prev => prev.map(pet =>
      pet.id === petId ? { ...pet, [field]: value } : pet
    ));
  };

  const onSubmit = (data: AppointmentFormValues) => {
    console.log("🚀 onSubmit called with data:", data);
    console.log("🚀 appointmentType:", appointmentType);
    console.log("🚀 searchQuery:", searchQuery);
    console.log("🚀 selectedClient:", selectedClient);
    console.log("🚀 selectedPet:", selectedPet);
    console.log("🚀 selectedCategories:", selectedCategories);

    // Validate appointment categories
    if (selectedCategories.length === 0) {
      toast({
        title: "Error",
        description: "Please select at least one appointment category",
        variant: "destructive",
      });
      return;
    }

    // Different validation for existing vs walk-in appointments
    if (appointmentType === 'existing') {
      if (!selectedClient || !selectedPet) {
        console.log("❌ Validation failed: Missing client or pet for existing appointment");
        toast({
          title: "Error",
          description: "Please select a client and pet",
          variant: "destructive",
        });
        return;
      }
    } else {
      // Walk-in validation
      if (!searchQuery.clientName) {
        console.log("❌ Validation failed: Missing client name");
        toast({
          title: "Error",
          description: "Please fill in client name",
          variant: "destructive",
        });
        return;
      }

      // Validate all pets
      const invalidPets = walkInPets.filter(pet => !pet.petName || !pet.speciesId);
      if (invalidPets.length > 0) {
        console.log("❌ Validation failed: Missing pet data", invalidPets);
        toast({
          title: "Error",
          description: "Please fill in pet name and species for all pets",
          variant: "destructive",
        });
        return;
      }
    }

    // Parse time string to create a full date-time
    let dateTime: Date;
    if (data.time === "now") {
      dateTime = new Date(); // Use current date and time
    } else {
      const [hours, minutes] = data.time.split(":").map(Number);
      dateTime = new Date(data.date);
      dateTime.setHours(hours, minutes, 0, 0);
    }

    // Calculate total estimated duration from selected categories
    const totalDuration = selectedCategories.reduce((total, category) => {
      return total + category.estimatedDuration;
    }, 0);

    // Create appointment data based on appointment type
    let appointmentData: any;

    const baseAppointmentData = {
      appointmentDate: dateTime,
      estimatedDuration: totalDuration || data.duration,
      status: data.status,
      priority: data.priority,
      generalNotes: data.notes,
      recommendations: data.reason,
      appointmentCategories: selectedCategories,
      staffInCharge: parseInt(data.vetId),
      clinicId: clinic?.clinicId || 1001,
    };

    if (appointmentType === 'existing') {
      appointmentData = {
        ...baseAppointmentData,
        clientId: parseInt(data.clientId!),
        petId: parseInt(data.petId!),
      };
    } else {
      // Walk-in appointment data with multiple pets
      appointmentData = {
        ...baseAppointmentData,
        clientName: searchQuery.clientName,
        phoneNumber: searchQuery.phone,
        isWalkIn: true,
        pets: walkInPets.map(pet => ({
          petName: pet.petName,
          speciesId: parseInt(pet.speciesId),
          breedId: pet.breedId ? parseInt(pet.breedId) : undefined,
          gender: pet.gender,
          age: pet.age ? parseInt(pet.age) : undefined,
          weight: pet.weight ? parseFloat(pet.weight) : undefined
        }))
      };
    }

    console.log("Submitting appointment data:", appointmentData);

    if (isEditMode) {
      updateAppointmentMutation.mutate(appointmentData);
    } else {
      createAppointmentMutation.mutate(appointmentData);
    }
  };

  return (
    <div className="p-8 rounded-lg shadow-sm">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">
          {isEditMode ? 'Edit Appointment' : 'Add New Appointment'}
        </h1>
      </div>

      {/* Appointment Type Selection */}
      <div className="mb-6">
        <Tabs value={appointmentType} onValueChange={(value) => setAppointmentType(value as 'existing' | 'walk-in')}>
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="existing" className="flex items-center gap-2">
              <Users className="h-4 w-4" />
              Existing Client
            </TabsTrigger>
            <TabsTrigger value="walk-in" className="flex items-center gap-2">
              <UserPlus className="h-4 w-4" />
              Walk-in
            </TabsTrigger>
          </TabsList>
        </Tabs>
      </div>

      <Form {...form}>
        <form onSubmit={(e) => {
          console.log("🔥 Form onSubmit event triggered!");
          e.preventDefault();
          form.handleSubmit(onSubmit, (errors) => {
            console.log("❌ Form validation errors:", errors);
            console.log("📋 Current form values:", form.getValues());
          })(e);
        }} className="space-y-6">
          <div className="grid grid-cols-2 gap-6 py-4">
            {/* Left Column - Client and Pet */}
            <div className="space-y-6">
              {appointmentType === 'existing' ? (
                /* Existing Client Search */
                <div>
                  <Label>Client</Label>
                  <div className="flex gap-2">
                    <Popover open={searchOpen.client} onOpenChange={(open) => setSearchOpen(prev => ({ ...prev, client: open }))}>
                      <PopoverTrigger asChild>
                        <Button variant="outline" className="w-full justify-start text-left font-normal">
                          <Search className="h-4 w-4 mr-2" />
                          {selectedClient ? `${selectedClient.firstName} ${selectedClient.lastName}` : "Search Client"}
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-80 p-3">
                        <div className="grid gap-4">
                          <div className="space-y-2">
                            <h4 className="font-medium leading-none">Search Client</h4>
                            <p className="text-sm text-muted-foreground">
                              Search by name or phone number
                            </p>
                          </div>
                          <div className="grid gap-2">
                            <div className="grid gap-1">
                              <Label htmlFor="clientName">Name</Label>
                              <Input
                                id="clientName"
                                placeholder="Enter client name"
                                value={searchQuery.clientName}
                                onChange={(e) => setSearchQuery(prev => ({
                                  ...prev,
                                  clientName: e.target.value
                                }))}
                              />
                            </div>
                          </div>
                          <Button
                            className="w-full"
                            onClick={handleClientSearch}
                          >
                            Search
                          </Button>
                          {clients.length > 0 && (
                            <div className="mt-2 max-h-48 overflow-y-auto">
                              {clients.map((client) => (
                                <Button
                                  key={client._id}
                                  variant="ghost"
                                  className="w-full justify-start text-left"
                                  onClick={() => handleClientSelect(client)}
                                >
                                  <div>
                                    <div className="font-medium">{client.firstName} {client.lastName}</div>
                                    <div className="text-sm text-muted-foreground">{client.phoneNumber}</div>
                                  </div>
                                </Button>
                              ))}
                            </div>
                          )}
                        </div>
                      </PopoverContent>
                    </Popover>
                    <Button
                      type="button"
                      variant="outline"
                      size="icon"
                      onClick={() => setShowQuickClientModal(true)}
                      title="Quick Add Client"
                    >
                      <UserPlus className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              ) : (
                /* Walk-in Client Form */
                <div className="space-y-4">
                  <Label>Walk-in Client Information</Label>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="walkInClientName">Client Name</Label>
                      <Input
                        id="walkInClientName"
                        placeholder="Enter client name"
                        value={searchQuery.clientName}
                        onChange={(e) => setSearchQuery(prev => ({
                          ...prev,
                          clientName: e.target.value
                        }))}
                      />
                    </div>
                    <div>
                      <Label htmlFor="walkInClientPhone">Phone Number</Label>
                      <Input
                        id="walkInClientPhone"
                        placeholder="Enter phone number"
                        value={searchQuery.phone}
                        onChange={(e) => setSearchQuery(prev => ({
                          ...prev,
                          phone: e.target.value
                        }))}
                      />
                    </div>
                  </div>
                </div>
              )}

              {/* Pet Selection */}
              <div>
                {appointmentType === 'existing' ? (
                  <div>
                    <Label>Pet</Label>
                    <div className="flex gap-2">
                      <FormField
                        control={form.control}
                        name="petId"
                        render={({ field }) => (
                          <FormItem className="flex-1">
                            <Select
                              onValueChange={(value) => {
                                field.onChange(value);
                                const pet = pets.find(p => p.petId?.toString() === value);
                                if (pet) setSelectedPet(pet);
                              }}
                              value={field.value}
                              disabled={!selectedClient}
                            >
                              <FormControl>
                                <SelectTrigger>
                                  <SelectValue placeholder="Select pet" />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                {pets.map((pet) => (
                                  <SelectItem key={pet.petId} value={pet.petId?.toString() || ""}>
                                    {(pet as any).petName || pet.name}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      <Button
                        type="button"
                        variant="outline"
                        size="icon"
                        onClick={() => setShowQuickPetModal(true)}
                        disabled={!selectedClient}
                        title="Quick Add Pet"
                      >
                        <PawPrint className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                ) : (
                  /* Walk-in Multiple Pets Form */
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <Label>Walk-in Pet Information</Label>
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={addWalkInPet}
                        className="flex items-center gap-2"
                      >
                        <PawPrint className="h-4 w-4" />
                        Add Pet
                      </Button>
                    </div>

                    {walkInPets.map((pet, index) => (
                      <div key={pet.id} className="border rounded-lg p-4 space-y-4">
                        <div className="flex items-center justify-between">
                          <h4 className="font-medium">Pet {index + 1}</h4>
                          {walkInPets.length > 1 && (
                            <Button
                              type="button"
                              variant="ghost"
                              size="sm"
                              onClick={() => removeWalkInPet(pet.id)}
                              className="text-red-500 hover:text-red-700"
                            >
                              <X className="h-4 w-4" />
                            </Button>
                          )}
                        </div>

                        <div className="grid grid-cols-2 gap-4">
                          <div>
                            <Label htmlFor={`walkInPetName-${pet.id}`}>Pet Name *</Label>
                            <Input
                              id={`walkInPetName-${pet.id}`}
                              placeholder="Enter pet name"
                              value={pet.petName}
                              onChange={(e) => updateWalkInPet(pet.id, 'petName', e.target.value)}
                            />
                          </div>
                          <div>
                            <Label htmlFor={`walkInPetSpecies-${pet.id}`}>Species *</Label>
                            <Select
                              value={pet.speciesId}
                              onValueChange={(value) => {
                                updateWalkInPet(pet.id, 'speciesId', value);
                                updateWalkInPet(pet.id, 'breedId', ''); // Reset breed
                              }}
                            >
                              <SelectTrigger>
                                <SelectValue placeholder="Select species" />
                              </SelectTrigger>
                              <SelectContent>
                                {species.map((s: any) => (
                                  <SelectItem key={s.speciesId} value={s.speciesId?.toString() || ""}>
                                    {s.speciesName || s.name}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                          </div>
                        </div>

                        <div className="grid grid-cols-2 gap-4">
                          <div>
                            <Label htmlFor={`walkInPetBreed-${pet.id}`}>Breed (Optional)</Label>
                            <Select
                              value={pet.breedId}
                              onValueChange={(value) => updateWalkInPet(pet.id, 'breedId', value)}
                              disabled={!pet.speciesId}
                            >
                              <SelectTrigger>
                                <SelectValue placeholder="Select breed" />
                              </SelectTrigger>
                              <SelectContent>
                                {breeds.map((breed: any) => (
                                  <SelectItem key={breed.breedId} value={breed.breedId?.toString() || ""}>
                                    {breed.breedName}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                          </div>
                          <div>
                            <Label htmlFor={`walkInPetGender-${pet.id}`}>Gender</Label>
                            <Select
                              value={pet.gender}
                              onValueChange={(value) => updateWalkInPet(pet.id, 'gender', value)}
                            >
                              <SelectTrigger>
                                <SelectValue placeholder="Select gender" />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="male">Male</SelectItem>
                                <SelectItem value="female">Female</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>
                        </div>

                        <div className="grid grid-cols-2 gap-4">
                          <div>
                            <Label htmlFor={`walkInPetAge-${pet.id}`}>Age (months)</Label>
                            <Input
                              id={`walkInPetAge-${pet.id}`}
                              type="number"
                              placeholder="Age in months"
                              value={pet.age}
                              onChange={(e) => updateWalkInPet(pet.id, 'age', e.target.value)}
                            />
                          </div>
                          <div>
                            <Label htmlFor={`walkInPetWeight-${pet.id}`}>Weight (kg)</Label>
                            <Input
                              id={`walkInPetWeight-${pet.id}`}
                              type="number"
                              step="0.1"
                              placeholder="Weight in kg"
                              value={pet.weight}
                              onChange={(e) => updateWalkInPet(pet.id, 'weight', e.target.value)}
                            />
                          </div>
                        </div>
                      </div>
                    ))
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Right Column - Date and Time */}
            <div className="space-y-6">
              {/* Date and Time in a grid */}
              <div className="grid grid-cols-1 gap-4">
                {/* Date Selection */}
                <FormField
                  control={form.control}
                  name="date"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Appointment Date</FormLabel>
                      <Popover>
                        <PopoverTrigger asChild>
                          <FormControl>
                            <Button
                              variant={"outline"}
                              className={cn(
                                "w-full pl-3 text-left font-normal",
                                !field.value && "text-muted-foreground"
                              )}
                            >
                              <CalendarIcon className="mr-2 h-4 w-4" />
                              {field.value ? (
                                format(field.value, "PPP")
                              ) : (
                                <span>Select date</span>
                              )}
                            </Button>
                          </FormControl>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-0" align="start">
                          <Calendar
                            mode="single"
                            selected={field.value}
                            onSelect={(date) => {
                              field.onChange(date);
                              handleDateSelect(date);
                            }}
                            initialFocus
                            disabled={(date) => date < new Date(new Date().setHours(0, 0, 0, 0))}
                          />
                        </PopoverContent>
                      </Popover>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Time Selection */}
                <FormField
                  control={form.control}
                  name="time"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Appointment Time</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        value={field.value}
                        disabled={!form.getValues("date")}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select time" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {timeOptions.map((time) => (
                            <SelectItem key={time} value={time}>
                              {time === "now" ? (
                                <div className="flex items-center gap-2">
                                  <Clock className="h-4 w-4" />
                                  Now ({format(new Date(), "HH:mm")})
                                </div>
                              ) : (
                                time
                              )}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>

            {/* Appointment Categories Selection */}
            <div className="col-span-2">
              <div className="space-y-4">
                <div>
                  <Label className="text-base font-semibold">Appointment Categories</Label>
                  <p className="text-sm text-gray-600">Select categories for this appointment. Services will be added in the workflow.</p>
                </div>
                <AppointmentCategorySelector
                  selectedCategories={selectedCategories}
                  onCategoriesChange={setSelectedCategories}
                  disabled={false}
                  mode="categories-only"
                />
              </div>
            </div>

            {/* Duration, Veterinarian, and Priority in one row for desktop */}
            <div className="col-span-2 grid grid-cols-1 md:grid-cols-3 gap-4">

              {/* Duration */}
              <FormField
                control={form.control}
                name="duration"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Duration (minutes)</FormLabel>
                    <FormControl>
                      <Input type="number" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Veterinarian Selection */}
              <FormField
                control={form.control}
                name="vetId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Veterinarian</FormLabel>
                    <Select onValueChange={field.onChange} value={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select veterinarian" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {staff.map((vet: any) => (
                          <SelectItem key={vet.staffId} value={vet.staffId?.toString() || ""}>
                            {vet.name ||
                             (vet.userId ? `${vet.userId.firstName || ''} ${vet.userId.lastName || ''}`.trim() : '') ||
                             (vet.firstName || vet.lastName ? `${vet.firstName || ''} ${vet.lastName || ''}`.trim() : '') ||
                             `Staff ID: ${vet.staffId}`}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Priority Selection */}
              <FormField
                control={form.control}
                name="priority"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Priority</FormLabel>
                    <Select onValueChange={field.onChange} value={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select priority" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="low">
                          <div className="flex items-center gap-2">
                            <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                            Low
                          </div>
                        </SelectItem>
                        <SelectItem value="normal">
                          <div className="flex items-center gap-2">
                            <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                            Normal
                          </div>
                        </SelectItem>
                        <SelectItem value="high">
                          <div className="flex items-center gap-2">
                            <div className="w-2 h-2 bg-orange-500 rounded-full"></div>
                            High
                          </div>
                        </SelectItem>
                        <SelectItem value="urgent">
                          <div className="flex items-center gap-2">
                            <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                            Urgent
                          </div>
                        </SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Reason for Visit */}
            <FormField
              control={form.control}
              name="reason"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Reason for Visit</FormLabel>
                  <FormControl>
                    <Input placeholder="Brief description of the visit reason" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Notes */}
            <FormField
              control={form.control}
              name="notes"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Additional Notes</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Any additional information about the appointment"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
          <div className="flex justify-end space-x-2">
            <Button
              type="button"
              variant="outline"
              onClick={() => navigate('/appointments')}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={form.formState.isSubmitting}
              onClick={() => console.log("🔥 Submit button clicked!")}
            >
              {form.formState.isSubmitting
                ? (isEditMode ? "Updating..." : "Creating...")
                : (isEditMode ? "Update Appointment" : "Create Appointment")
              }
            </Button>
          </div>
        </form>
      </Form>

      {/* Quick Add Modals */}
      <QuickClientModal
        isOpen={showQuickClientModal}
        onClose={() => setShowQuickClientModal(false)}
        onClientCreated={handleQuickClientCreated}
      />

      <QuickPetModal
        isOpen={showQuickPetModal}
        onClose={() => setShowQuickPetModal(false)}
        onPetCreated={handleQuickPetCreated}
        clientId={(selectedClient?.clientId || "").toString()}
      />
    </div>
  );
};

export default AddAppointment;
