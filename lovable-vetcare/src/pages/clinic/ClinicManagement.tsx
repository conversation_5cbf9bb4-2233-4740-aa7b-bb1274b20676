import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Ta<PERSON>, Ta<PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/components/ui/use-toast';
import { useAuth } from '@/store';
import { api } from '@/services/api';
import { 
  Building2, 
  Users, 
  Settings, 
  Shield, 
  Clock,
  MapPin,
  Phone,
  Mail,
  Globe,
  Plus,
  Edit,
  Trash2,
  UserPlus,
  Save
} from 'lucide-react';
import { ClinicProfileManager } from '@/components/profile/ClinicProfileManager';
import { ClinicStaffManagement } from '@/components/staff/ClinicStaffManagement';

interface ClinicSettings {
  operatingHours: {
    monday: { open: string; close: string; isOpen: boolean };
    tuesday: { open: string; close: string; isOpen: boolean };
    wednesday: { open: string; close: string; isOpen: boolean };
    thursday: { open: string; close: string; isOpen: boolean };
    friday: { open: string; close: string; isOpen: boolean };
    saturday: { open: string; close: string; isOpen: boolean };
    sunday: { open: string; close: string; isOpen: boolean };
  };
  appointmentSettings: {
    defaultDuration: number;
    allowOnlineBooking: boolean;
    requireDeposit: boolean;
    cancellationPolicy: string;
  };
  notificationSettings: {
    emailNotifications: boolean;
    smsNotifications: boolean;
    reminderHours: number;
  };
}

const ClinicManagement: React.FC = () => {
  const { currentClinic, staff: currentUser } = useAuth();
  const { toast } = useToast();
  const [loading, setLoading] = useState(false);
  const [clinic, setClinic] = useState(currentClinic);
  const [settings, setSettings] = useState<ClinicSettings | null>(null);
  const [activeTab, setActiveTab] = useState('overview');

  // Check if user is clinic owner or manager
  const isClinicOwner = currentUser?.isClinicOwner || false;
  const isClinicManager = currentUser?.roleId === 2; // Assuming 2 is manager role
  const canManageClinic = isClinicOwner || isClinicManager;

  useEffect(() => {
    if (currentClinic) {
      setClinic(currentClinic);
      fetchClinicSettings();
    }
  }, [currentClinic]);

  const fetchClinicSettings = async () => {
    if (!currentClinic?.clinicId) return;
    
    setLoading(true);
    try {
      const response = await api.get(`/clinics/${currentClinic.clinicId}/settings`);
      if (response.data?.success) {
        setSettings(response.data.data);
      }
    } catch (error: any) {
      console.error('Error fetching clinic settings:', error);
      toast({
        variant: 'destructive',
        title: 'Error',
        description: 'Failed to load clinic settings',
      });
    } finally {
      setLoading(false);
    }
  };

  const updateClinicSettings = async (newSettings: Partial<ClinicSettings>) => {
    if (!currentClinic?.clinicId) return;
    
    setLoading(true);
    try {
      const response = await api.put(`/clinics/${currentClinic.clinicId}/settings`, newSettings);
      if (response.data?.success) {
        setSettings(prev => ({ ...prev, ...newSettings } as ClinicSettings));
        toast({
          title: 'Success',
          description: 'Clinic settings updated successfully',
        });
      }
    } catch (error: any) {
      toast({
        variant: 'destructive',
        title: 'Error',
        description: error.message || 'Failed to update clinic settings',
      });
    } finally {
      setLoading(false);
    }
  };

  if (!canManageClinic) {
    return (
      <div className="container mx-auto p-6">
        <Card>
          <CardContent className="p-6 text-center">
            <Shield className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h2 className="text-xl font-semibold text-gray-900 mb-2">Access Denied</h2>
            <p className="text-gray-600">You don't have permission to manage clinic settings.</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!clinic) {
    return (
      <div className="container mx-auto p-6">
        <Card>
          <CardContent className="p-6 text-center">
            <Building2 className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h2 className="text-xl font-semibold text-gray-900 mb-2">No Clinic Selected</h2>
            <p className="text-gray-600">Please select a clinic to manage.</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 max-w-7xl">
      {/* Header */}
      <div className="mb-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 flex items-center gap-2">
              <Building2 className="h-8 w-8" />
              Clinic Management
            </h1>
            <p className="text-gray-600 mt-1">
              Manage {clinic.clinicName} settings, staff, and operations
            </p>
          </div>
          <Badge variant="outline" className="text-sm">
            {isClinicOwner ? 'Clinic Owner' : 'Clinic Manager'}
          </Badge>
        </div>
      </div>

      {/* Clinic Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <Card>
          <CardContent className="p-4 text-center">
            <Building2 className="h-8 w-8 text-blue-600 mx-auto mb-2" />
            <div className="text-2xl font-bold text-gray-900">{clinic.clinicName}</div>
            <div className="text-sm text-gray-600">Clinic Name</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <Users className="h-8 w-8 text-green-600 mx-auto mb-2" />
            <div className="text-2xl font-bold text-gray-900">-</div>
            <div className="text-sm text-gray-600">Staff Members</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <Clock className="h-8 w-8 text-purple-600 mx-auto mb-2" />
            <div className="text-2xl font-bold text-gray-900">-</div>
            <div className="text-sm text-gray-600">Today's Appointments</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <Settings className="h-8 w-8 text-orange-600 mx-auto mb-2" />
            <div className="text-2xl font-bold text-gray-900">Active</div>
            <div className="text-sm text-gray-600">Status</div>
          </CardContent>
        </Card>
      </div>

      {/* Management Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview" className="flex items-center gap-2">
            <Building2 className="h-4 w-4" />
            Overview
          </TabsTrigger>
          <TabsTrigger value="staff" className="flex items-center gap-2">
            <Users className="h-4 w-4" />
            Staff Management
          </TabsTrigger>
          <TabsTrigger value="settings" className="flex items-center gap-2">
            <Settings className="h-4 w-4" />
            Settings
          </TabsTrigger>
          <TabsTrigger value="permissions" className="flex items-center gap-2">
            <Shield className="h-4 w-4" />
            Permissions
          </TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <ClinicProfileManager 
            clinic={clinic} 
            onUpdate={setClinic}
            isOwner={isClinicOwner}
          />
        </TabsContent>

        <TabsContent value="staff" className="space-y-6">
          <ClinicStaffManagement />
        </TabsContent>

        <TabsContent value="settings" className="space-y-6">
          {/* Operating Hours */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Clock className="h-5 w-5" />
                Operating Hours
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'].map((day) => (
                  <div key={day} className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="flex items-center gap-3">
                      <input
                        type="checkbox"
                        id={day}
                        className="rounded"
                        defaultChecked={true}
                      />
                      <label htmlFor={day} className="font-medium capitalize">
                        {day}
                      </label>
                    </div>
                    <div className="flex items-center gap-2 text-sm">
                      <input
                        type="time"
                        defaultValue="09:00"
                        className="border rounded px-2 py-1"
                      />
                      <span>to</span>
                      <input
                        type="time"
                        defaultValue="17:00"
                        className="border rounded px-2 py-1"
                      />
                    </div>
                  </div>
                ))}
              </div>
              <div className="mt-4">
                <Button>
                  <Save className="h-4 w-4 mr-2" />
                  Save Operating Hours
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Appointment Settings */}
          <Card>
            <CardHeader>
              <CardTitle>Appointment Settings</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-2">Default Appointment Duration</label>
                  <select className="w-full border rounded-lg px-3 py-2">
                    <option value="15">15 minutes</option>
                    <option value="30" selected>30 minutes</option>
                    <option value="45">45 minutes</option>
                    <option value="60">60 minutes</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium mb-2">Reminder Hours Before</label>
                  <select className="w-full border rounded-lg px-3 py-2">
                    <option value="1">1 hour</option>
                    <option value="2">2 hours</option>
                    <option value="24" selected>24 hours</option>
                    <option value="48">48 hours</option>
                  </select>
                </div>
              </div>
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <label className="text-sm font-medium">Allow Online Booking</label>
                  <input type="checkbox" className="rounded" defaultChecked />
                </div>
                <div className="flex items-center justify-between">
                  <label className="text-sm font-medium">Require Deposit</label>
                  <input type="checkbox" className="rounded" />
                </div>
                <div className="flex items-center justify-between">
                  <label className="text-sm font-medium">Email Notifications</label>
                  <input type="checkbox" className="rounded" defaultChecked />
                </div>
                <div className="flex items-center justify-between">
                  <label className="text-sm font-medium">SMS Notifications</label>
                  <input type="checkbox" className="rounded" />
                </div>
              </div>
              <div className="mt-4">
                <Button>
                  <Save className="h-4 w-4 mr-2" />
                  Save Appointment Settings
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="permissions" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Shield className="h-5 w-5" />
                Role & Permission Management
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                {/* Quick Actions */}
                <div className="flex flex-wrap gap-3">
                  <Button variant="outline" size="sm">
                    <UserPlus className="h-4 w-4 mr-2" />
                    Invite Staff Member
                  </Button>
                  <Button variant="outline" size="sm">
                    <Plus className="h-4 w-4 mr-2" />
                    Create Custom Role
                  </Button>
                  <Button variant="outline" size="sm">
                    <Settings className="h-4 w-4 mr-2" />
                    Manage Permissions
                  </Button>
                </div>

                {/* Role Overview */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <Card className="border-l-4 border-l-blue-500">
                    <CardContent className="p-4">
                      <div className="flex items-center justify-between">
                        <div>
                          <h4 className="font-semibold">Veterinarians</h4>
                          <p className="text-sm text-gray-600">Full medical access</p>
                        </div>
                        <Badge variant="secondary">3</Badge>
                      </div>
                    </CardContent>
                  </Card>
                  <Card className="border-l-4 border-l-green-500">
                    <CardContent className="p-4">
                      <div className="flex items-center justify-between">
                        <div>
                          <h4 className="font-semibold">Nurses</h4>
                          <p className="text-sm text-gray-600">Assist with procedures</p>
                        </div>
                        <Badge variant="secondary">5</Badge>
                      </div>
                    </CardContent>
                  </Card>
                  <Card className="border-l-4 border-l-purple-500">
                    <CardContent className="p-4">
                      <div className="flex items-center justify-between">
                        <div>
                          <h4 className="font-semibold">Receptionists</h4>
                          <p className="text-sm text-gray-600">Front desk operations</p>
                        </div>
                        <Badge variant="secondary">2</Badge>
                      </div>
                    </CardContent>
                  </Card>
                </div>

                {/* Permission Categories */}
                <div className="space-y-4">
                  <h4 className="font-semibold">Permission Categories</h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {[
                      { name: 'Appointment Management', permissions: ['View', 'Create', 'Edit', 'Cancel'] },
                      { name: 'Client Management', permissions: ['View', 'Create', 'Edit', 'Delete'] },
                      { name: 'Medical Records', permissions: ['View', 'Create', 'Edit', 'Delete'] },
                      { name: 'Billing & Invoicing', permissions: ['View', 'Create', 'Edit', 'Process'] },
                      { name: 'Inventory Management', permissions: ['View', 'Create', 'Edit', 'Manage'] },
                      { name: 'Reports & Analytics', permissions: ['View', 'Generate', 'Export'] }
                    ].map((category, index) => (
                      <Card key={index}>
                        <CardContent className="p-4">
                          <h5 className="font-medium mb-3">{category.name}</h5>
                          <div className="flex flex-wrap gap-2">
                            {category.permissions.map((permission, pIndex) => (
                              <Badge key={pIndex} variant="outline" className="text-xs">
                                {permission}
                              </Badge>
                            ))}
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default ClinicManagement;
