import mongoose from "mongoose";
import bcrypt from "bcryptjs";
import User from "../models/user.model.js";
import Role from "../models/role.model.js";
import jwt from "jsonwebtoken";
import { JWT_EXPIRES_IN, JWT_SECRET } from "../config/env.js";
import { sendResponse } from "../utils/responseHandler.js";
import { authService } from "../services/auth.service.js";

// Login
export const login = async (req, res) => {
    try {
        const { email, password } = req.body;
        const userAgent = req.headers['user-agent'];
        const ipAddress = req.ip || req.connection.remoteAddress;

        // Special case for the default admin email
        const isAdminLogin = email === '<EMAIL>';

        // Find the user in the User table
        const user = await User.findOne({ email })
            .select('firstName middleName lastName email phoneNumber password status lastLogin loginCount roleId -_id')
            .lean();

        if (!user) {
            // Log failed login attempt for security monitoring
            console.log(`Failed login attempt for email: ${email} from IP: ${ipAddress}`);
            return sendResponse(res, 404, false, "User not found");
        }

        // Verify password
        const isPasswordValid = await bcrypt.compare(password, user.password);
        if (!isPasswordValid) {
            // Log failed login attempt
            await User.updateOne(
                { _id: user._id },
                {
                    $push: {
                        loginHistory: {
                            timestamp: new Date(),
                            ipAddress,
                            userAgent,
                            status: 'failed'
                        }
                    }
                }
            );
            return sendResponse(res, 401, false, "Invalid credentials");
        }

        // Update login tracking
        await User.updateOne(
            { _id: user._id },
            {
                $set: { lastLogin: new Date() },
                $inc: { loginCount: 1 },
                $push: {
                    loginHistory: {
                        timestamp: new Date(),
                        ipAddress,
                        userAgent,
                        status: 'success'
                    }
                }
            }
        );

        // Generate token using auth service
        const token = await authService.generateToken(user, false);

        // Prepare response
        const responseData = {
            token,
            user: {
                ...user,
                password: undefined
            }
        };

        return sendResponse(res, 200, true, "Login successful", responseData);
    } catch (error) {
        return sendResponse(res, 500, false, error.message);
    }
};

// Create user
export const createUser = async (req, res) => {
    try {
        const {
            firstName,
            middleName,
            lastName,
            email,
            phoneNumber,
            address,
            dob,
            roleId
        } = req.body;

        // Check if user already exists
        const existingUser = await User.findOne({ email });
        if (existingUser) {
            return sendResponse(res, 409, false, "User already exists");
        }

        // create password
        const password = 'pass123';
        // const password = Math.random().toString(36).slice(-8);

        // Hash password
        const salt = await bcrypt.genSalt(10);
        const hashedPassword = await bcrypt.hash(password, salt);

        // Create user
        const user = await User.create({
            firstName,
            middleName,
            lastName,
            email,
            phoneNumber,
            password: hashedPassword,
            address,
            dob,
            roleId
        });

        return sendResponse(res, 201, true, "User created successfully", user);
    } catch (error) {
        return sendResponse(res, 500, false, error.message);
    }
};

// Get all users
export const getAllUsers = async (req, res) => {
    try {
        const {
            page = 1,
            limit = 10,
            sortBy = "createdAt",
            sortOrder = "desc",
            search,
            status
        } = req.query;

        const skip = (parseInt(page) - 1) * parseInt(limit);
        const sortOptions = { [sortBy]: sortOrder === "desc" ? -1 : 1 };

        let query = {};
        if (status !== undefined) query.status = parseInt(status);
        if (search) {
            query.$or = [
                { firstName: { $regex: search, $options: "i" } },
                { lastName: { $regex: search, $options: "i" } },
                { phoneNumber: { $regex: search, $options: "i" } },
                { email: { $regex: search, $options: "i" } }
            ];
        }

        const [users, totalCount] = await Promise.all([
            User.find(query)
                .select('-password -_id')
                .sort(sortOptions)
                .skip(skip)
                .limit(parseInt(limit))
                .lean(),
            User.countDocuments(query)
        ]);

        const roles = await Role.find().select('-_id').lean();
        const roleMap = roles.reduce((map, role) => {
            map[role.roleId] = {
                roleId: role.roleId,
                name: role.name,
                description: role.description
            };
            return map;
        }, {});

        const usersWithRoles = users.map(user => ({
            ...user,
            role: roleMap[user.roleId] || null
        }));

        return sendResponse(res, 200, true, "Users retrieved successfully", {
            data: usersWithRoles,
            pagination: {
                totalCount: totalCount,
                page: parseInt(page),
                limit: parseInt(limit),
                offset: (parseInt(page) - 1) * parseInt(limit),
                totalPages: Math.ceil(totalCount / limit)
            }
        });
    } catch (error) {
        return sendResponse(res, 500, false, error.message);
    }
};

// Update user
export const updateUser = async (req, res) => {
    try {
        const {
            firstName,
            middleName,
            lastName,
            phoneNumber,
            email,
            oldPassword,
            newPassword,
            dob,
            status,
            isClinicOwner,
            roleId
        } = req.body;

        const { userId } = req.params;

        // Check if userId is a number (new auto-increment ID) or string (MongoDB ObjectId)
        let query = {};
        if (!isNaN(userId)) {
            query.userId = parseInt(userId);
        } else {
            query._id = userId;
        }

        const user = await User.findOne(query);
        if (!user) {
            return sendResponse(res, 404, false, "User not found");
        }

        if (oldPassword && newPassword) {
            const isMatch = await bcrypt.compare(oldPassword, user.password);
            if (!isMatch) {
                return sendResponse(res, 400, false, "Old password is incorrect");
            }
            const salt = await bcrypt.genSalt(10);
            user.password = await bcrypt.hash(newPassword, salt);
        }

        // Update fields if provided
        if (firstName) user.firstName = firstName;
        if (middleName) user.middleName = middleName;
        if (lastName) user.lastName = lastName;
        if (phoneNumber) user.phoneNumber = phoneNumber;
        if (email) user.email = email;
        if (dob) user.dob = dob;
        if (status !== undefined) user.status = status;
        if (isClinicOwner !== undefined) user.isClinicOwner = isClinicOwner;
        if (roleId) {
            const roleExists = await Role.findOne({ roleId }).lean();
            if (!roleExists) {
                return sendResponse(res, 400, false, "Invalid role ID");
            }
            user.roleId = roleId;
        }

        await user.save();
        const updatedUser = await User.findById(user._id)
            .select('-password')
            .lean();

        return sendResponse(res, 200, true, "User updated successfully", updatedUser);
    } catch (error) {
        return sendResponse(res, 500, false, error.message);
    }
};