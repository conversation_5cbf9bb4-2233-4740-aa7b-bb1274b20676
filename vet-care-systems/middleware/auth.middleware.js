import jwt from 'jsonwebtoken';
import crypto from 'crypto';
import Role from '../models/role.model.js';
import Staff from '../models/staff.model.js';
import Client from '../models/client.model.js';
import User from '../models/user.model.js';
import Clinic from '../models/clinic.model.js';
import { JWT_SECRET } from '../config/env.js';
import { sendResponse } from '../utils/responseHandler.js';
// Note: authService and cacheService imports removed as they're not used in this middleware

/**
 * Middleware to verify JWT token and attach user ID to request
 * This middleware only validates the token and passes the user ID forward
 */
export const verifyToken = async (req, res, next) => {
    try {
        // TEMPORARY: Skip auth for development debugging
        if (process.env.NODE_ENV === 'development' && process.env.SKIP_AUTH === 'true') {
            console.log(`🔓 DEVELOPMENT: Skipping auth for ${req.method} ${req.originalUrl}`);
            req.user = {
                userId: 1001, // Use numeric ID for consistency
                email: '<EMAIL>',
                userType: 'staff', // Change to staff for appointment creation
                roleId: 1001,
                roleName: 'developer',
                clinicId: 1005,
                isClinicOwner: false,
                staffId: 1001, // Set numeric staffId for appointment creation
                firstName: 'Dev',
                lastName: 'User',
                jobTitle: 'Developer'
            };
            return next();
        }

        // Extract token from Authorization header
        const authHeader = req.headers.authorization;

        if (!authHeader || authHeader === 'Bearer null' || authHeader === 'Bearer undefined') {
            console.log(`❌ ${req.method} ${req.originalUrl} - No authorization header`);
            return sendResponse(res, 401, false, 'No token provided');
        }

        const token = authHeader.split(' ')[1];
        console.log(`🔐 Extracted token: ${token.substring(0, 20)}...`);

        if (!token || token === 'null' || token === 'undefined') {
            return sendResponse(res, 401, false, 'No token provided');
        }

        // Verify token
        const decoded = jwt.verify(token, JWT_SECRET);
        console.log(`🔐 Token decoded:`, {
            userId: decoded.userId,
            staffId: decoded.staffId,
            email: decoded.email,
            userType: decoded.userType
        });

        // Extract user ID from token (could be userId or staffId)
        const userId = decoded.userId || decoded.staffId;

        console.log(`🔐 ${req.method} ${req.originalUrl} - Token verified for user:`, userId, 'Type:', decoded.userType);

        // Check if token has valid user identifier
        if (!userId) {
            console.log('❌ Token missing user identifier:', decoded);
            return sendResponse(res, 401, false, 'Invalid token format');
        }

        // Initialize user object with clinic context
        req.user = {
            userId: userId,
            email: decoded.email,
            userType: decoded.userType,
            roleId: decoded.roleId,
            roleName: decoded.roleName,
            clinicId: decoded.clinicId || null,
            isClinicOwner: decoded.isClinicOwner || false,
            staffId: decoded.staffId || null
        };

        // Single verification logic for both users and staff
        let userRecord = null;
        let staffRecord = null;

        // Check if this is a staff token (has staffId)
        if (decoded.staffId) {
            req.user.staffId = decoded.staffId;

            // Look up staff record - handle both ObjectId and numeric ID formats
            if (typeof decoded.staffId === 'string' && decoded.staffId.length === 24) {
                // MongoDB ObjectId format
                staffRecord = await Staff.findById(decoded.staffId)
                    .select('_id staffId status clinicId isClinicOwner roleId')
                    .populate('roleId', 'roleName')
                    .lean();
            } else {
                // Numeric ID format
                staffRecord = await Staff.findOne({ staffId: decoded.staffId })
                    .select('_id staffId status clinicId isClinicOwner roleId')
                    .populate('roleId', 'roleName')
                    .lean();
            }

            if (staffRecord && staffRecord.status === 1) {
                // Staff exists and is active
                req.user.staffId = staffRecord.staffId || decoded.staffId;
                req.user.clinicId = decoded.clinicId || staffRecord.currentClinicId || staffRecord.clinicId;
                req.user.userType = 'staff';
                req.user.isClinicOwner = staffRecord.isClinicOwner || false;
                req.user.roleId = staffRecord.roleId?._id || staffRecord.roleId;
                req.user.roleName = staffRecord.roleId?.roleName || decoded.roleName;

                // Add additional clinic access info
                req.user.primaryClinicId = staffRecord.clinicId;
                req.user.additionalClinics = staffRecord.additionalClinics || [];
                req.user.hasMultiClinicAccess = staffRecord.additionalClinics && staffRecord.additionalClinics.length > 0;

                console.log('✅ Staff authenticated:', req.user.staffId, 'Role:', req.user.roleName, 'Clinic:', req.user.clinicId);
                next();
                return;
            }
        }

        // If not staff or staff lookup failed, check user table
        const lookupUserId = decoded.userId || userId;

        if (typeof lookupUserId === 'string' && lookupUserId.length === 24) {
            // MongoDB ObjectId format
            userRecord = await User.findById(lookupUserId)
                .select('_id userId status roleId')
                .populate('roleId', 'roleName')
                .lean();
        } else {
            // Numeric ID format
            userRecord = await User.findOne({ userId: lookupUserId })
                .select('_id userId status roleId')
                .populate('roleId', 'roleName')
                .lean();
        }

        if (userRecord && userRecord.status === 1) {
            // User exists and is active
            req.user.userId = userRecord.userId;
            req.user.userType = 'user';
            req.user.roleId = userRecord.roleId?._id || userRecord.roleId;
            req.user.roleName = userRecord.roleId?.roleName || decoded.roleName;
            console.log('✅ User authenticated:', req.user.userId);
            next();
            return;
        }

        // If we get here, either the user/staff doesn't exist or is inactive
        console.log('❌ Authentication failed - user/staff not found or inactive');
        return sendResponse(res, 401, false, 'Invalid or expired token');

    } catch (error) {
        console.error('Token verification error:', error);
        if (error.name === 'JsonWebTokenError') {
            return sendResponse(res, 401, false, 'Invalid token');
        } else if (error.name === 'TokenExpiredError') {
            return sendResponse(res, 401, false, 'Token expired');
        } else {
            return sendResponse(res, 500, false, 'Authentication error: ' + error.message);
        }
    }
};

/**
 * Middleware to check if the user is a system admin
 * Provides full system access to admin users
 */
export const isAdmin = async (req, res, next) => {
    try {
        const user = req.user;

        if (!user) {
            return sendResponse(res, 401, false, 'Authentication required');
        }

        // Check if user is the system admin
        const isSystemAdmin = user.email === '<EMAIL>' ||
                             user.roleId === 1001 || // super_admin role
                             user.roleName === 'super_admin';

        if (isSystemAdmin) {
            console.log(`🔑 Admin access granted for ${user.email || user.userId}`);
            return next();
        }

        return sendResponse(res, 403, false, 'Admin access required');
    } catch (error) {
        console.error('Admin check error:', error);
        return sendResponse(res, 500, false, 'Error checking admin access');
    }
};

/**
 * Middleware to check if the user has specific permissions
 * NOTE: This middleware is temporarily disabled and will be reimplemented later
 * @param {Array} requiredPermissions - Array of permission IDs
 */
export const hasPermission = (requiredPermissions) => {
    return async (req, res, next) => {
        // Simply pass through for now - permission checks will be implemented later
        next();
    };
};

/**
 * Middleware to check if the user has access to a specific clinic
 * NOTE: This middleware is temporarily disabled and will be reimplemented later
 * @param {String} clinicIdParam - The parameter name that contains the clinic ID
 */
export const hasClinicAccess = (clinicIdParam = 'clinicId') => {
    return async (req, res, next) => {
        // Simply pass through for now - clinic access checks will be implemented later
        next();
    };
};

/**
 * Middleware to check if the user is a clinic owner
 * NOTE: This middleware is temporarily disabled and will be reimplemented later
 * @param {String} clinicIdParam - The parameter name that contains the clinic ID
 */
export const isClinicOwner = (clinicIdParam = 'clinicId') => {
    return async (req, res, next) => {
        // Simply pass through for now - clinic owner checks will be implemented later
        next();
    };
};

