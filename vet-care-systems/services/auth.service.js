import jwt from 'jsonwebtoken';
import bcrypt from 'bcryptjs';
import crypto from 'crypto';
import User from '../models/user.model.js';
import Staff from '../models/staff.model.js';
import Role from '../models/role.model.js';
import Permission from '../models/permission.model.js';
import cacheService from './cache.service.js';
import queueService from './queue.service.js';
import { JWT_SECRET, JWT_EXPIRES_IN, NODE_ENV } from '../config/env.js';

class AuthService {
  constructor() {
    this.maxLoginAttempts = 5;
    this.lockoutDuration = 15 * 60; // 15 minutes in seconds
    this.otpExpiration = 5 * 60; // 5 minutes in seconds
  }

  /**
   * Enhanced login with rate limiting and OTP support
   */
  async login(email, password, otpCode = null, userType = 'auto') {
    try {
      // Check rate limiting
      const rateLimitKey = `login_attempts:${email}`;
      const attempts = await cacheService.get(rateLimitKey) || 0;
      
      if (attempts >= this.maxLoginAttempts) {
        throw new Error('Account temporarily locked due to too many failed attempts');
      }

      let user = null;
      let isStaff = false;

      // Auto-detect user type or use specified type
      if (userType === 'auto' || userType === 'staff') {
        user = await Staff.findOne({ email }).populate('roleId');
        if (user) isStaff = true;
      }

      if (!user && (userType === 'auto' || userType === 'user')) {
        user = await User.findOne({ email }).populate('roleId');
      }

      if (!user) {
        await this.incrementLoginAttempts(email);
        throw new Error('Invalid credentials');
      }

      // Verify password
      const isValidPassword = await bcrypt.compare(password, user.password);
      if (!isValidPassword) {
        await this.incrementLoginAttempts(email);
        throw new Error('Invalid credentials');
      }

      // Check if user is active
      if (user.status !== 1) {
        throw new Error('Account is not active');
      }

      // Check if OTP is required
      const requiresOtp = user.requiresOtp || user.roleId?.requiresOtp || false;
      
      if (requiresOtp && !otpCode) {
        // Generate and send OTP
        const otp = this.generateOTP();
        await this.sendOTP(user, otp);
        
        return {
          requiresOtp: true,
          userId: user.userId || user.staffId,
          message: 'OTP sent to your registered contact'
        };
      }

      // Verify OTP if provided
      if (otpCode && !await this.verifyOTP(user.userId || user.staffId, otpCode)) {
        throw new Error('Invalid OTP');
      }

      // Clear login attempts on successful login
      await cacheService.del(rateLimitKey);

      // Generate JWT token
      const token = await this.generateToken(user, isStaff);

      // Store session in cache
      const sessionData = await this.createSession(user, isStaff);
      await cacheService.setSession(user.userId || user.staffId, sessionData);

      // Log successful login
      console.log(`✅ Successful login: ${email} (${isStaff ? 'staff' : 'user'})`);

      return {
        token,
        user: {
          id: user.userId || user.staffId,
          email: user.email,
          name: user.name,
          userType: isStaff ? 'staff' : 'user',
          role: user.roleId?.roleName,
          clinicId: isStaff ? user.clinicId : null,
          isClinicOwner: isStaff ? user.isClinicOwner : false
        },
        sessionData
      };
    } catch (error) {
      console.error('Login error:', error);
      throw error;
    }
  }

  /**
   * Generate secure JWT token
   */
  async generateToken(user, isStaff = false) {
    const payload = {
      userId: user.userId || user.staffId || user._id,
      email: user.email,
      userType: isStaff ? 'staff' : 'user',
      roleId: user.roleId?._id || user.roleId,
      roleName: user.roleId?.roleName,
      iat: Math.floor(Date.now() / 1000)
    };

    if (isStaff) {
      payload.staffId = user.staffId || user._id;
      payload.clinicId = user.clinicId || user.primaryClinicId;
      payload.isClinicOwner = user.isClinicOwner || false;
    }

    return jwt.sign(payload, JWT_SECRET, {
      expiresIn: JWT_EXPIRES_IN,
      issuer: 'vetcare-api',
      audience: 'vetcare-app'
    });
  }

  /**
   * Create session data
   */
  async createSession(user, isStaff = false) {
    const permissions = await this.getUserPermissions(user.userId || user.staffId, isStaff);
    
    return {
      userId: user.userId || user.staffId,
      email: user.email,
      name: user.name,
      userType: isStaff ? 'staff' : 'user',
      role: user.roleId?.roleName,
      permissions,
      clinicId: isStaff ? user.clinicId : null,
      isClinicOwner: isStaff ? user.isClinicOwner : false,
      loginTime: new Date().toISOString(),
      lastActivity: new Date().toISOString()
    };
  }

  /**
   * Get user permissions with caching
   */
  async getUserPermissions(userId, isStaff = false) {
    const cacheKey = `permissions:${userId}`;
    let permissions = await cacheService.get(cacheKey);

    if (!permissions) {
      const userModel = isStaff ? Staff : User;
      const user = await userModel.findOne({ 
        [isStaff ? 'staffId' : 'userId']: userId 
      }).populate({
        path: 'roleId',
        populate: {
          path: 'permissions',
          model: 'Permission'
        }
      });

      if (user && user.roleId) {
        permissions = user.roleId.permissions.map(p => ({
          id: p.permissionId,
          name: p.permissionName,
          category: p.category
        }));

        // Cache permissions for 30 minutes
        await cacheService.set(cacheKey, permissions, 1800);
      } else {
        permissions = [];
      }
    }

    return permissions;
  }

  /**
   * Validate user permission
   */
  async validateUserPermission(userId, requiredPermission, isStaff = false) {
    if (!requiredPermission) return true;

    const permissions = await this.getUserPermissions(userId, isStaff);
    return permissions.some(p => p.name === requiredPermission);
  }

  /**
   * Generate OTP
   */
  generateOTP() {
    return Math.floor(100000 + Math.random() * 900000).toString();
  }

  /**
   * Send OTP via email/SMS
   */
  async sendOTP(user, otp) {
    const otpKey = `otp:${user.userId || user.staffId}`;
    
    // Store OTP in cache
    await cacheService.set(otpKey, {
      code: otp,
      attempts: 0,
      createdAt: new Date().toISOString()
    }, this.otpExpiration);

    // Send OTP via email
    if (user.email) {
      await queueService.addJob('notifications', 'email', {
        to: user.email,
        subject: 'Your OTP Code',
        body: `Your OTP code is: ${otp}. This code will expire in 5 minutes.`,
        priority: 'high'
      });
    }

    // Send OTP via SMS if phone number is available
    if (user.phone) {
      await queueService.addJob('notifications', 'sms', {
        to: user.phone,
        message: `Your VetCare OTP code is: ${otp}. Expires in 5 minutes.`,
        priority: 'high'
      });
    }

    console.log(`📱 OTP sent to user ${user.userId || user.staffId}: ${otp}`);
  }

  /**
   * Verify OTP
   */
  async verifyOTP(userId, providedOtp) {
    const otpKey = `otp:${userId}`;
    const otpData = await cacheService.get(otpKey);

    if (!otpData) {
      return false; // OTP expired or not found
    }

    // Check attempts
    if (otpData.attempts >= 3) {
      await cacheService.del(otpKey);
      return false; // Too many attempts
    }

    if (otpData.code === providedOtp) {
      // OTP is correct, remove it
      await cacheService.del(otpKey);
      return true;
    } else {
      // Increment attempts
      otpData.attempts++;
      await cacheService.set(otpKey, otpData, this.otpExpiration);
      return false;
    }
  }

  /**
   * Increment login attempts for rate limiting
   */
  async incrementLoginAttempts(email) {
    const rateLimitKey = `login_attempts:${email}`;
    const attempts = await cacheService.increment(rateLimitKey);
    
    if (attempts === 1) {
      // Set expiration on first attempt
      await cacheService.set(rateLimitKey, attempts, this.lockoutDuration);
    }

    return attempts;
  }

  /**
   * Logout user
   */
  async logout(userId) {
    try {
      // Remove session from cache
      await cacheService.deleteSession(userId);
      await cacheService.invalidateUserPermissions(userId);
      
      console.log(`👋 User ${userId} logged out`);
      return true;
    } catch (error) {
      console.error('Logout error:', error);
      return false;
    }
  }

  /**
   * Refresh token
   */
  async refreshToken(oldToken) {
    try {
      const decoded = jwt.verify(oldToken, JWT_SECRET, { ignoreExpiration: true });
      
      // Check if token is not too old (max 7 days)
      const tokenAge = Date.now() / 1000 - decoded.iat;
      if (tokenAge > 7 * 24 * 60 * 60) {
        throw new Error('Token too old for refresh');
      }

      // Get fresh user data
      const userModel = decoded.userType === 'staff' ? Staff : User;
      const user = await userModel.findOne({
        [decoded.userType === 'staff' ? 'staffId' : 'userId']: decoded.userId
      }).populate('roleId');

      if (!user || user.status !== 1) {
        throw new Error('User not found or inactive');
      }

      // Generate new token
      const newToken = await this.generateToken(user, decoded.userType === 'staff');
      
      return { token: newToken };
    } catch (error) {
      console.error('Token refresh error:', error);
      throw error;
    }
  }

  /**
   * Validate session
   */
  async validateSession(userId) {
    const session = await cacheService.getSession(userId);
    
    if (!session) {
      return null;
    }

    // Update last activity
    session.lastActivity = new Date().toISOString();
    await cacheService.setSession(userId, session);

    return session;
  }

  /**
   * Health check
   */
  async healthCheck() {
    try {
      const cacheHealth = await cacheService.healthCheck();
      const queueHealth = await queueService.healthCheck();

      return {
        status: 'healthy',
        cache: cacheHealth.status,
        queue: queueHealth.status,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        error: error.message,
        timestamp: new Date().toISOString()
      };
    }
  }
}

// Create singleton instance
const authService = new AuthService();

export default authService;
export { authService };
